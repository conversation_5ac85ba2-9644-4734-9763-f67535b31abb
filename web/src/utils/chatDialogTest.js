/**
 * 聊天弹窗测试工具
 * 用于测试聊天弹窗的打开和群组列表获取功能
 */

/**
 * 测试群组列表API调用
 */
export const testGroupListAPI = async () => {
  try {
    console.log('🧪 开始测试群组列表API...')
    
    // 动态导入API函数
    const { getGroupList } = await import('@/api/im/group.js')
    
    const params = {
      page: 1,
      pageSize: 100
    }
    
    console.log('📤 发送请求，参数:', params)
    const response = await getGroupList(params)
    
    console.log('📥 API响应:', response)
    
    // 检查响应结构
    const responseData = response.data || response
    console.log('响应数据结构:', {
      hasCode: 'code' in responseData,
      code: responseData.code,
      hasData: 'data' in responseData,
      dataType: typeof responseData.data,
      hasList: responseData.data && 'list' in responseData.data,
      listLength: responseData.data?.list?.length || 0
    })
    
    return {
      success: true,
      response: responseData,
      groupCount: responseData.data?.list?.length || 0
    }
  } catch (error) {
    console.error('❌ 群组列表API测试失败:', error)
    return {
      success: false,
      error: error.message,
      details: error
    }
  }
}

/**
 * 测试用户信息状态
 */
export const testUserInfo = async () => {
  try {
    console.log('🧪 开始测试用户信息状态...')
    
    const { useUserStore } = await import('@/pinia/modules/user.js')
    const userStore = useUserStore()
    
    const userInfo = {
      hasToken: !!userStore.token,
      hasFormChatId: !!userStore.formChatId,
      hasUserInfo: !!userStore.userInfo,
      formChatId: userStore.formChatId,
      userId: userStore.userInfo?.id || userStore.userInfo?.ID,
      nickname: userStore.userInfo?.nickname
    }
    
    console.log('👤 用户信息状态:', userInfo)
    
    return {
      success: true,
      userInfo
    }
  } catch (error) {
    console.error('❌ 用户信息测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试聊天管理器状态
 */
export const testChatManagerStatus = async () => {
  try {
    console.log('🧪 开始测试聊天管理器状态...')
    
    const { chatManager } = await import('./chatManager.js')
    
    const status = {
      isInitialized: chatManager.isInitialized,
      canSaveGroups: typeof chatManager.saveGroupList === 'function',
      canGetGroups: typeof chatManager.getGroupList === 'function'
    }
    
    console.log('💬 聊天管理器状态:', status)
    
    // 如果未初始化，尝试初始化
    if (!status.isInitialized) {
      console.log('🔄 尝试初始化聊天管理器...')
      await chatManager.init()
      status.isInitialized = chatManager.isInitialized
      console.log('✅ 聊天管理器初始化完成')
    }
    
    return {
      success: true,
      status
    }
  } catch (error) {
    console.error('❌ 聊天管理器测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 模拟聊天弹窗打开流程
 */
export const simulateChatDialogOpen = async () => {
  try {
    console.log('🧪 开始模拟聊天弹窗打开流程...')
    
    // 1. 检查用户信息
    console.log('1️⃣ 检查用户信息...')
    const userResult = await testUserInfo()
    if (!userResult.success) {
      throw new Error('用户信息检查失败: ' + userResult.error)
    }
    
    // 2. 检查聊天管理器
    console.log('2️⃣ 检查聊天管理器...')
    const managerResult = await testChatManagerStatus()
    if (!managerResult.success) {
      throw new Error('聊天管理器检查失败: ' + managerResult.error)
    }
    
    // 3. 测试群组列表API
    console.log('3️⃣ 测试群组列表API...')
    const apiResult = await testGroupListAPI()
    if (!apiResult.success) {
      throw new Error('群组列表API测试失败: ' + apiResult.error)
    }
    
    // 4. 测试群组列表存储
    console.log('4️⃣ 测试群组列表存储...')
    if (apiResult.response?.data?.list?.length > 0) {
      const { chatManager } = await import('./chatManager.js')
      await chatManager.saveGroupList(apiResult.response.data.list)
      console.log('✅ 群组列表存储成功')
      
      // 验证存储结果
      const savedGroups = await chatManager.getGroupList()
      console.log('✅ 存储验证成功，群组数量:', savedGroups.length)
    }
    
    console.log('🎉 聊天弹窗打开流程模拟完成')
    
    return {
      success: true,
      results: {
        userInfo: userResult,
        chatManager: managerResult,
        groupListAPI: apiResult
      }
    }
  } catch (error) {
    console.error('❌ 聊天弹窗打开流程模拟失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 检查聊天弹窗相关的所有依赖
 */
export const checkChatDialogDependencies = async () => {
  console.log('🔍 开始检查聊天弹窗依赖...')
  
  const checks = {
    userStore: false,
    chatManager: false,
    groupAPI: false,
    database: false
  }
  
  try {
    // 检查用户Store
    const { useUserStore } = await import('@/pinia/modules/user.js')
    const userStore = useUserStore()
    checks.userStore = !!userStore
    console.log('✅ 用户Store检查通过')
  } catch (error) {
    console.error('❌ 用户Store检查失败:', error.message)
  }
  
  try {
    // 检查聊天管理器
    const { chatManager } = await import('./chatManager.js')
    checks.chatManager = !!chatManager
    console.log('✅ 聊天管理器检查通过')
  } catch (error) {
    console.error('❌ 聊天管理器检查失败:', error.message)
  }
  
  try {
    // 检查群组API
    const { getGroupList } = await import('@/api/im/group.js')
    checks.groupAPI = typeof getGroupList === 'function'
    console.log('✅ 群组API检查通过')
  } catch (error) {
    console.error('❌ 群组API检查失败:', error.message)
  }
  
  try {
    // 检查数据库
    const { isDatabaseConnected } = await import('./db.js')
    checks.database = isDatabaseConnected()
    console.log('✅ 数据库检查通过')
  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message)
  }
  
  const allPassed = Object.values(checks).every(check => check)
  
  console.log('📊 依赖检查结果:', checks)
  console.log(allPassed ? '🎉 所有依赖检查通过' : '⚠️ 部分依赖检查失败')
  
  return {
    allPassed,
    checks
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.testGroupAPI = testGroupListAPI
  window.testUserInfo = testUserInfo
  window.testChatManager = testChatManagerStatus
  window.simulateDialogOpen = simulateChatDialogOpen
  window.checkDialogDeps = checkChatDialogDependencies
  
  console.log('🧪 聊天弹窗测试工具已加载:')
  console.log('  - window.testGroupAPI() - 测试群组列表API')
  console.log('  - window.testUserInfo() - 测试用户信息状态')
  console.log('  - window.testChatManager() - 测试聊天管理器状态')
  console.log('  - window.simulateDialogOpen() - 模拟弹窗打开流程')
  console.log('  - window.checkDialogDeps() - 检查所有依赖')
}
