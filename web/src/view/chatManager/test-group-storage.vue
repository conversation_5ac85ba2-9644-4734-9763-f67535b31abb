<template>
  <div class="test-page p-6">
    <h2 class="text-2xl font-bold mb-4">群组数据存储测试页面</h2>
    
    <div class="space-y-4">
      <div class="bg-blue-50 p-4 rounded-lg">
        <h3 class="font-semibold mb-2">测试说明</h3>
        <p class="text-sm text-gray-600">
          此页面用于测试群组列表获取、存储到IndexedDB以及聊天记录查询功能
        </p>
      </div>

      <div class="flex space-x-4">
        <el-button type="primary" @click="testFetchGroups" :loading="loading">
          测试获取群组列表
        </el-button>
        
        <el-button type="success" @click="testStorageQuery" :loading="queryLoading">
          测试存储查询
        </el-button>
        
        <el-button type="warning" @click="testChatFlow" :loading="chatLoading">
          测试聊天流程
        </el-button>
        
        <el-button type="danger" @click="clearStorage">
          清空存储
        </el-button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- API响应数据 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold mb-2">API响应数据</h3>
          <pre class="text-xs bg-white p-2 rounded overflow-auto max-h-60">{{ apiResponse }}</pre>
        </div>

        <!-- IndexedDB存储数据 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold mb-2">IndexedDB存储数据</h3>
          <pre class="text-xs bg-white p-2 rounded overflow-auto max-h-60">{{ storageData }}</pre>
        </div>

        <!-- 聊天记录测试 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold mb-2">聊天记录测试</h3>
          <pre class="text-xs bg-white p-2 rounded overflow-auto max-h-60">{{ chatTestResult }}</pre>
        </div>

        <!-- 测试日志 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold mb-2">测试日志</h3>
          <div class="text-xs bg-white p-2 rounded overflow-auto max-h-60">
            <div v-for="(log, index) in testLogs" :key="index" class="mb-1">
              <span :class="getLogClass(log.type)">[{{ log.time }}]</span>
              <span class="ml-2">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getGroupList } from '@/api/im/group.js'
import { chatManager } from '@/utils/chatManager.js'
import { useUserStore } from '@/pinia/modules/user.js'

defineOptions({
  name: 'TestGroupStorage'
})

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const queryLoading = ref(false)
const chatLoading = ref(false)
const apiResponse = ref('')
const storageData = ref('')
const chatTestResult = ref('')
const testLogs = ref([])

// 添加日志
const addLog = (type, message) => {
  testLogs.value.push({
    type,
    time: new Date().toLocaleTimeString(),
    message
  })
}

// 获取日志样式类
const getLogClass = (type) => {
  switch (type) {
    case 'success': return 'text-green-600'
    case 'error': return 'text-red-600'
    case 'warning': return 'text-yellow-600'
    default: return 'text-blue-600'
  }
}

// 测试获取群组列表
const testFetchGroups = async () => {
  loading.value = true
  try {
    addLog('info', '开始测试获取群组列表...')
    
    // 确保用户信息已获取
    if (!userStore.formChatId) {
      await userStore.GetChatUserInfo()
    }
    
    const params = { page: 1, pageSize: 100 }
    const response = await getGroupList(params)
    
    apiResponse.value = JSON.stringify(response, null, 2)
    addLog('success', `API调用成功，获取到 ${response.data?.data?.list?.length || 0} 个群组`)
    
    // 保存到IndexedDB
    if (response.data?.code === 0 && response.data?.data?.list) {
      await chatManager.saveGroupList(response.data.data.list)
      addLog('success', '群组列表已保存到IndexedDB')
    }
    
  } catch (error) {
    addLog('error', `获取群组列表失败: ${error.message}`)
    ElMessage.error('获取群组列表失败')
  } finally {
    loading.value = false
  }
}

// 测试存储查询
const testStorageQuery = async () => {
  queryLoading.value = true
  try {
    addLog('info', '开始测试存储查询...')
    
    // 初始化chatManager
    await chatManager.init()
    
    // 获取存储的群组列表
    const savedGroups = await chatManager.getGroupList()
    storageData.value = JSON.stringify(savedGroups, null, 2)
    
    addLog('success', `从IndexedDB查询到 ${savedGroups.length} 个群组`)
    
    // 获取所有聊天列表
    const allChats = await chatManager.getAllChats()
    addLog('info', `所有聊天列表: ${allChats.length} 个`)
    
  } catch (error) {
    addLog('error', `存储查询失败: ${error.message}`)
    ElMessage.error('存储查询失败')
  } finally {
    queryLoading.value = false
  }
}

// 测试聊天流程
const testChatFlow = async () => {
  chatLoading.value = true
  try {
    addLog('info', '开始测试聊天流程...')
    
    // 获取第一个群组
    const savedGroups = await chatManager.getGroupList()
    if (savedGroups.length === 0) {
      addLog('warning', '没有找到群组，请先获取群组列表')
      return
    }
    
    const firstGroup = savedGroups[0]
    addLog('info', `使用群组: ${firstGroup.name} (ID: ${firstGroup.chatId})`)
    
    // 发送测试消息
    const testMessage = {
      id: `test_${Date.now()}`,
      fromid: userStore.formChatId || 10000,
      toid: firstGroup.chatId,
      chatid: firstGroup.chatId,
      msg: `测试消息 - ${new Date().toLocaleString()}`,
      typecode: 2,
      typecode2: 0,
      t: new Date().toISOString(),
      timestamp: Date.now(),
      senderNickname: '测试用户',
      senderAvatar: '/static/My/avatar.jpg'
    }
    
    await chatManager.sendGroupMessage(testMessage)
    addLog('success', '测试消息发送成功')
    
    // 查询聊天记录
    const messages = await chatManager.getGroupMessages(firstGroup.chatId, 1, 10)
    chatTestResult.value = JSON.stringify(messages, null, 2)
    
    addLog('success', `查询到 ${messages.length} 条聊天记录`)
    
  } catch (error) {
    addLog('error', `聊天流程测试失败: ${error.message}`)
    ElMessage.error('聊天流程测试失败')
  } finally {
    chatLoading.value = false
  }
}

// 清空存储
const clearStorage = async () => {
  try {
    addLog('info', '清空存储数据...')
    
    // 这里可以添加清空IndexedDB的逻辑
    apiResponse.value = ''
    storageData.value = ''
    chatTestResult.value = ''
    testLogs.value = []
    
    addLog('success', '存储数据已清空')
    ElMessage.success('存储数据已清空')
    
  } catch (error) {
    addLog('error', `清空存储失败: ${error.message}`)
    ElMessage.error('清空存储失败')
  }
}
</script>

<style scoped>
.test-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
