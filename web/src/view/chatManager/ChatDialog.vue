<template>
  <el-dialog
    v-model="visible"
    title=""
    :width="1200"
    :height="750"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="chat-dialog"
    destroy-on-close
  >
    <template #header>
      <div class="dialog-header flex justify-between items-center">
        <div class="header-left">
        </div>
        <div class="header-right">
          <el-button @click="closeDialog" type="danger" size="small" circle>
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <div class="chat-container flex h-full bg-gray-800">
      <!-- 左侧会话列表 -->
      <div class="conversation-sidebar w-[350px] bg-gray-900 border-r border-gray-700 flex flex-row">
        <div class="sidebar-tabs w-16 flex flex-col bg-gray-950 flex-shrink-0">
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'groups' }"
            @click="activeTab = 'groups'"
          >
            <el-icon class="text-lg"><ChatLineRound /></el-icon>
            <span class="text-xs font-medium text-center">群聊</span>
          </div>
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'friends' }"
            @click="activeTab = 'friends'"
          >
            <el-icon class="text-lg"><User /></el-icon>
            <span class="text-xs font-medium text-center">私聊</span>
          </div>
        </div>

        <div class="conversation-list flex-1 overflow-y-auto bg-gray-900">
          <!-- 群组会话 -->
          <div v-if="activeTab === 'groups'" class="conversation-section p-2 h-full">
            <div v-if="groupConversations.length === 0" class="empty-state text-center text-gray-400 py-8">
              <p>正在加载群组列表...</p>
              <p class="text-xs mt-2">群组数量: {{ groupConversations.length }}</p>
              <p class="text-xs mt-1">当前标签页: {{ activeTab }}</p>
              <p class="text-xs mt-1">数据状态: {{ JSON.stringify(groupConversations) }}</p>
            </div>

            <!-- <div v-else class="text-xs text-gray-400 mb-2">
              找到 {{ groupConversations.length }} 个群组
            </div> -->

            <ConversationCard
              v-for="group in groupConversations"
              :key="group.id"
              :conversation="group"
              :is-selected="selectedConversation?.id === group.id"
              @click="selectConversation(group)"
            />
          </div>

          <!-- 好友会话 -->
          <div v-if="activeTab === 'friends'" class="conversation-section p-2 h-full">
            <div v-if="friendConversations.length === 0" class="empty-state text-center text-gray-400 py-8">
              <p>暂无好友会话</p>
              <p class="text-xs mt-2">在群聊中点击成员头像开始私聊</p>
            </div>

            <ConversationCard
              v-for="friend in friendConversations"
              :key="friend.id"
              :conversation="friend"
              :is-selected="selectedConversation?.id === friend.id"
              @click="selectConversation(friend)"
            />
          </div>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-main flex-1 flex flex-col bg-gray-700">
        <MessagePanel
          v-if="selectedConversation"
          :conversation="selectedConversation"
          @start-chat="handleStartPrivateChat"
        />
        <div v-else class="empty-chat flex items-center justify-center h-full rounded-lg">
          <el-empty description="请选择一个会话开始聊天" />
        </div>
      </div>

      <!-- 群组成员面板 -->
      <div v-if="selectedConversation?.type === 'group'" class="group-members bg-gray-800 border-l border-gray-600">
        <GroupUserPanel
          :group="selectedConversation?.originalData"
          @start-chat="handleStartPrivateChat"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import ConversationCard from './components/ConversationCard.vue'
import MessagePanel from './components/MessagePanel.vue'
import GroupUserPanel from './components/GroupUserPanel.vue'
import { getGroupList } from '@/api/im/group.js'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { useUserStore } from '@/pinia/modules/user.js'
import { chatManager } from '@/utils/chatManager.js'
import { getChatMessages } from '@/utils/db.js'

defineOptions({
  name: 'ChatDialog'
})

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentUser = ref(props.user)
const activeTab = ref('groups')
const selectedConversation = ref(null)
const webSocketStore = useWebSocketStore()
const userStore = useUserStore()

// 群组会话数据
const groupConversations = ref([])

const friendConversations = ref([])

// 方法
const selectConversation = (conversation) => {
  selectedConversation.value = conversation
}

const closeDialog = () => {
  visible.value = false
  emit('close')
}

// 处理开始私聊事件
const handleStartPrivateChat = async (privateChatData) => {
  console.log('处理私聊事件:', privateChatData)

  try {
    // 检查是否已存在该好友会话
    const targetUserId = privateChatData.originalData?.id || privateChatData.originalData?.ID || privateChatData.originalData?.userId
    const existingFriend = friendConversations.value.find(
      friend => (friend.originalData?.id || friend.originalData?.ID) === targetUserId
    )

    if (existingFriend) {
      // 如果已存在，直接切换到该会话
      console.log('好友会话已存在，直接切换:', existingFriend)
      activeTab.value = 'friends'
      selectedConversation.value = existingFriend
    } else {
      // 使用聊天管理器创建私聊
      const userInfo = {
        id: targetUserId,
        name: privateChatData.name || privateChatData.originalData?.nickname,
        nickname: privateChatData.name || privateChatData.originalData?.nickname,
        avatar: privateChatData.avatar || privateChatData.originalData?.avatar
      }

      // 添加到私聊列表
      await chatManager.startPrivateChat(userInfo)
      console.log('私聊已添加到数据库')

      // 添加到好友会话列表
      friendConversations.value.unshift(privateChatData)

      // 切换到好友标签页并选择该会话
      activeTab.value = 'friends'
      selectedConversation.value = privateChatData

      console.log('好友会话创建成功:', privateChatData)
    }
  } catch (error) {
    console.error('处理私聊事件失败:', error)
    ElMessage.error('创建私聊会话失败')
  }
}




// 获取群聊的最后一条消息
const getGroupLastMessage = async (groupId) => {
  try {
    const messages = await getChatMessages(groupId.toString(), 1, 1)

    if (messages && messages.length > 0) {
      const lastMessage = messages[0]
      return {
        content: lastMessage.msg || lastMessage.lastMessage || '',
        time: lastMessage.t || lastMessage.timestamp,
        type: lastMessage.typecode2 || 0
      }
    }
    return null
  } catch (error) {
    console.warn('获取群聊最后一条消息失败:', error)
    return null
  }
}

// 获取群聊的未读消息数量
const getGroupUnreadCount = async (groupId) => {
  try {
    // 从Pinia store或本地存储获取未读数量
    const chatList = webSocketStore.chatList || {}
    const chatInfo = chatList[groupId.toString()]

    if (chatInfo && chatInfo.unreadCount) {
      return chatInfo.unreadCount
    }

    // 如果store中没有，尝试从数据库计算未读数量
    // 这里可以根据实际需求实现更复杂的未读计算逻辑
    return 0
  } catch (error) {
    console.warn('获取群聊未读数量失败:', error)
    return 0
  }
}

// 格式化消息内容显示
const formatMessageContent = (content, type) => {
  if (!content) return '暂无消息'

  // 根据消息类型格式化显示内容
  switch (type) {
    case 1: // 音频
      return '[语音]'
    case 2: // 图片
      return '[图片]'
    case 3: // 视频
      return '[语音]'
    case 4: // 转发消息
      return '[转发消息]'
    case 5: // 撤回
      return '[消息已撤回]'
    case 9: // 语音通话
      return '[语音通话]'
    case 10: // 视频通话
      return '[视频通话]'
    default: // 文本消息
      // 限制显示长度
      return content.length > 30 ? content.substring(0, 30) + '...' : content
  }
}

// 获取群组列表
const fetchGroupList = async () => {
  try {
    console.log('🔄 开始获取群组列表...')
    console.log('📊 当前用户信息:', {
      formChatId: userStore.formChatId,
      userInfo: userStore.userInfo,
      token: userStore.token ? '已设置' : '未设置'
    })

    const params = {
      page: 1,
      pageSize: 100
    }

    console.log('📤 发送群组列表请求，参数:', params)
    const response = await getGroupList(params)

    console.log('📥 API 获取群组列表响应:', response)

    // 检查响应数据结构
    const responseData = response.data || response
    console.log('响应数据:', responseData)
    console.log('响应数据类型:', typeof responseData)
    console.log('响应数据code:', responseData.code)
    console.log('响应数据data:', responseData.data)

    if (responseData.code == 0 && responseData.data && responseData.data.list) {
      console.log('✅ 群组数据获取成功:', responseData.data)
      console.log('📋 群组列表:', responseData.data.list)
      console.log('📊 群组数量:', responseData.data.list.length)

      const groupList = responseData.data.list

      // 🔄 立即存储群组列表到IndexedDB（作为聊天列表）
      try {
        console.log('💾 开始保存群组列表到IndexedDB...')
        await chatManager.saveGroupList(groupList)
        console.log('✅ 群组列表已成功保存到IndexedDB，可用于聊天查询')

        // 验证数据是否正确保存
        await verifyGroupDataStorage()
      } catch (dbError) {
        console.error('❌ 保存群组列表到数据库失败:', dbError)
        ElMessage.error('保存群组数据失败')
      }

      // 转换群组数据为UI显示格式
      const groupsData = groupList.map((group) => {
        console.log('🔄 处理群组数据:', {
          id: group.id,
          name: group.groupName,
          avatar: group.groupHeader,
          userCount: group.userId?.length || 0,
          admins: group.admins?.length || 0
        })

        return {
          id: `group_${group.id}`,
          type: 'group',
          name: group.groupName || `群组${group.id}`,
          avatar: group.groupHeader || '/static/My/avatar.jpg',
          lastMessage: '暂无消息',
          lastTime: formatTime(group.createdAt),
          unread: 0,
          online: true,
          // 保存完整的原始群组数据，用于后续聊天功能
          originalData: {
            ...group,
            // 确保ID字段统一
            ID: group.id,
            // 确保名称字段统一
            nickname: group.groupName,
            groupId: group.id
          }
        }
      })

      // 更新本地群组会话列表（用于UI显示）
      groupConversations.value = groupsData
      console.log('✅ UI群组数据转换完成:', groupConversations.value.length, '个群组')

      // 如果当前是群聊标签页且有群组数据，自动选择第一个群组
      if (activeTab.value === 'groups' && groupConversations.value.length > 0) {
        selectedConversation.value = groupConversations.value[0]
        console.log('🎯 自动选择第一个群组:', selectedConversation.value.name)
      }
    } else {
      console.log('API 响应格式不正确或无数据:', responseData)
      console.log('尝试其他可能的数据结构...')

      // 尝试其他可能的数据结构
      let groupList = null
      if (responseData.list) {
        groupList = responseData.list
      } else if (responseData.data && Array.isArray(responseData.data)) {
        groupList = responseData.data
      } else if (Array.isArray(responseData)) {
        groupList = responseData
      }

      if (groupList && groupList.length > 0) {
        console.log('找到群组列表:', groupList)
        const groupsData = groupList.map((group) => {
          console.log('处理群组:', group)
          return {
            id: `group_${group.id}`,
            type: 'group',
            name: group.groupName || group.name || `群组${group.id}`,
            avatar: group.groupHeader || group.avatar || '',
            lastMessage: '暂无消息',
            lastTime: formatTime(group.createdAt || new Date()),
            unread: 0,
            online: true,
            originalData: group
          }
        })

        // 更新本地群组会话列表
        groupConversations.value = groupsData
        console.log('备用方案转换后的群组数据:', groupConversations.value)

        // 存储群组列表到IndexedDB
        try {
          await chatManager.saveGroupList(groupList)
          console.log('✅ 群组列表已保存到IndexedDB（备用方案）')

          // 验证数据是否正确保存
          await verifyGroupDataStorage()
        } catch (dbError) {
          console.error('❌ 保存群组列表到数据库失败（备用方案）:', dbError)
        }
      } else {
        console.log('⚠️ 未找到群组数据')
      }
    }

    console.log('📊 fetchGroupList 执行完成，当前群组数量:', groupConversations.value.length)
  } catch (error) {
    console.error('❌ 获取群组列表失败:', error)
    console.error('错误详情:', error.message)
    console.error('错误堆栈:', error.stack)
    throw error // 重新抛出错误，让调用方知道失败了
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  if (messageDate.getTime() === today.getTime()) {
    // 今天的消息显示时间
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    // 昨天的消息
    return '昨天'
  } else {
    // 更早的消息显示日期
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 验证群组数据是否正确保存并可查询
const verifyGroupDataStorage = async () => {
  try {
    console.log('🔍 验证群组数据存储...')

    // 1. 从IndexedDB获取保存的群组列表
    const savedGroups = await chatManager.getGroupList()
    console.log('📋 从IndexedDB获取的群组列表:', savedGroups)

    if (savedGroups.length > 0) {
      // 2. 验证第一个群组的数据结构
      const firstGroup = savedGroups[0]
      console.log('🔍 验证群组数据结构:', {
        id: firstGroup.id,
        chatId: firstGroup.chatId,
        name: firstGroup.name,
        type: firstGroup.type,
        typecode: firstGroup.typecode
      })

      // 3. 测试通过群组ID查询聊天记录（应该返回空数组，因为还没有聊天记录）
      const chatMessages = await chatManager.getGroupMessages(firstGroup.chatId, 1, 10)
      console.log('💬 群组聊天记录查询测试:', {
        groupId: firstGroup.chatId,
        messageCount: chatMessages.length,
        messages: chatMessages
      })

      console.log('✅ 群组数据存储验证通过，可以正常查询')
      return true
    } else {
      console.warn('⚠️ 没有找到保存的群组数据')
      return false
    }
  } catch (error) {
    console.error('❌ 群组数据存储验证失败:', error)
    return false
  }
}

// 测试群聊消息存储和查询功能
const testGroupChatFlow = async (groupId) => {
  try {
    console.log('🧪 开始测试群聊消息流程，群组ID:', groupId)

    // 1. 模拟发送一条群聊消息
    const testMessage = {
      id: `test_msg_${Date.now()}`,
      fromid: userStore.formChatId || 10000,
      toid: groupId,
      chatid: groupId,
      msg: '这是一条测试消息，验证群聊存储功能',
      typecode: 2, // 群聊类型
      typecode2: 0, // 文本消息
      t: new Date().toISOString(),
      timestamp: Date.now(),
      senderNickname: '测试用户',
      senderAvatar: '/static/My/avatar.jpg'
    }

    console.log('📤 发送测试消息:', testMessage)
    await chatManager.sendGroupMessage(testMessage)
    console.log('✅ 测试消息发送成功')

    // 2. 查询该群组的聊天记录
    const messages = await chatManager.getGroupMessages(groupId, 1, 10)
    console.log('📥 查询群聊记录结果:', {
      groupId,
      messageCount: messages.length,
      latestMessage: messages[0]
    })

    // 3. 验证消息是否正确保存
    const isMessageSaved = messages.some(msg => msg.msg === testMessage.msg)
    if (isMessageSaved) {
      console.log('✅ 群聊消息存储和查询功能正常')
      return true
    } else {
      console.warn('⚠️ 测试消息未找到，可能存储失败')
      return false
    }

  } catch (error) {
    console.error('❌ 群聊消息流程测试失败:', error)
    return false
  }
}

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    // 从用户状态获取token
    const token = userStore.token

    if (!token) {
      console.warn('用户未登录或token不存在，无法初始化WebSocket连接')
      return
    }

    if (!webSocketStore.isConnected) {
      console.log('初始化WebSocket连接...')
      await webSocketStore.initConnection(token, true)
      console.log('WebSocket连接已建立')
    } else {
      console.log('WebSocket已连接')
    }
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
}

// 从IndexedDB加载聊天列表
const loadChatListFromDB = async () => {
  try {
    console.log('从IndexedDB加载聊天列表...')

    // 初始化聊天管理器
    await chatManager.init()

    // 获取所有聊天列表
    const allChats = await chatManager.getAllChats()
    console.log('从数据库加载的聊天列表:', allChats)

    if (allChats && allChats.length > 0) {
      // 将数据库中的聊天列表转换为组件格式
      const dbGroupConversations = []
      const dbFriendConversations = []

      // 使用Set来去重，避免重复的聊天记录
      const existingGroupIds = new Set(groupConversations.value.map(g => g.originalData?.id || g.originalData?.ID))
      const existingFriendIds = new Set(friendConversations.value.map(f => f.id.replace('friend_', '')))

      allChats.forEach(chat => {
        const isGroup = chat.type === 'group'
        const chatId = chat.chatId

        // 避免重复添加
        if (isGroup && existingGroupIds.has(chatId)) return
        if (!isGroup && existingFriendIds.has(chatId.toString())) return

        const conversation = {
          id: isGroup ? `group_${chatId}` : `friend_${chatId}`,
          type: isGroup ? 'group' : 'friend',
          name: chat.name || `${isGroup ? '群聊' : '用户'}${chatId}`,
          avatar: chat.avatar || '/static/My/avatar.jpg',
          lastMessage: chat.lastMessage || '暂无消息',
          lastTime: formatTime(chat.timestamp || chat.lastTime),
          unread: chat.unreadCount || 0,
          online: true,
          // 保存原始数据
          originalData: {
            id: chatId,
            ID: chatId, // 保持向后兼容
            nickname: chat.name,
            avatar: chat.avatar,
            phone: ''
          }
        }

        if (isGroup) {
          dbGroupConversations.push(conversation)
          existingGroupIds.add(chatId)
        } else {
          dbFriendConversations.push(conversation)
          existingFriendIds.add(chatId.toString())
        }
      })

      // 合并数据库数据和现有数据
      if (dbGroupConversations.length > 0) {
        groupConversations.value = [...groupConversations.value, ...dbGroupConversations]
        console.log('合并后的群组会话:', groupConversations.value)
      }

      if (dbFriendConversations.length > 0) {
        friendConversations.value = [...friendConversations.value, ...dbFriendConversations]
        console.log('合并后的好友会话:', friendConversations.value)
      }

      // 如果当前是群聊标签页且有群组数据，自动选择第一个群组
      if (activeTab.value === 'groups' && groupConversations.value.length > 0 && !selectedConversation.value) {
        selectedConversation.value = groupConversations.value[0]
        console.log('自动选择第一个群组:', selectedConversation.value)
      }
    }
  } catch (error) {
    console.error('从数据库加载聊天列表失败:', error)
  }
}

// 组件挂载时初始化基础功能
onMounted(async () => {
  console.log('组件已挂载，开始初始化...')

  // 确保获取了聊天用户信息
  if (!userStore.formChatId) {
    console.log('获取聊天用户信息...')
    await userStore.GetChatUserInfo()
  }

  // 初始化WebSocket
  await initWebSocket()

  // 只加载数据库中的聊天列表，不主动获取API数据
  // API数据将在打开弹窗时获取
  await loadChatListFromDB()

  console.log('组件基础初始化完成')
})

// 监听用户变化
watch(() => props.user, (newUser) => {
  currentUser.value = newUser
}, { immediate: true })

// 监听弹窗显示状态变化
watch(visible, async (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    console.log('🚀 弹窗打开，开始初始化聊天数据...')
    fetchGroupList()

    try {
      // 确保获取了聊天用户信息
      if (!userStore.formChatId) {
        console.log('📋 获取聊天用户信息...')
        await userStore.GetChatUserInfo()
      }

      console.log('📂 开始加载聊天数据...')

      // 1. 先加载数据库中的聊天列表（快速显示）
      await loadChatListFromDB()
      console.log('✅ 数据库聊天列表加载完成')

      // 2. 获取最新的群组列表（从API）
      console.log('🔄 开始获取群组列表...')
      try {
        await fetchGroupList()
        console.log('✅ 群组列表获取完成')
      } catch (fetchError) {
        console.error('❌ 群组列表获取失败:', fetchError)
        ElMessage.warning('获取群组列表失败，将显示缓存数据')
      }

      // 3. 如果有群组数据，自动选择第一个
      if (groupConversations.value.length > 0) {
        selectedConversation.value = groupConversations.value[0]
        console.log('🎯 自动选择第一个群组:', selectedConversation.value.name)
      } else {
        console.log('⚠️ 没有找到群组数据')
      }

      console.log('🎉 聊天弹窗初始化完成')
    } catch (error) {
      console.error('❌ 初始化聊天弹窗失败:', error)
      ElMessage.error('初始化聊天失败，请重试')
    }
  }
})

// 更新群聊列表中的最后一条消息
const updateGroupLastMessage = (groupId, messageData) => {
  console.log('开始更新群聊最后消息:', { groupId, messageData })

  // 确保groupId是字符串或数字类型
  const targetGroupId = String(groupId)

  const groupIndex = groupConversations.value.findIndex(
    group => String(group.originalData?.id || group.originalData?.ID) === targetGroupId
  )

  console.log('查找群聊索引:', { targetGroupId, groupIndex, totalGroups: groupConversations.value.length })

  if (groupIndex !== -1) {
    const group = groupConversations.value[groupIndex]
    const oldLastMessage = group.lastMessage
    const oldLastTime = group.lastTime

    // 更新最后消息内容和时间
    group.lastMessage = formatMessageContent(
      messageData.msg || messageData.content,
      messageData.typecode2 || messageData.type || 0
    )
    group.lastTime = formatTime(messageData.t || messageData.timestamp || new Date())

    // 如果不是当前选中的群聊，增加未读数量
    if (selectedConversation.value?.id !== group.id) {
      group.unread = (group.unread || 0) + 1
      console.log('增加未读数量:', { groupId: targetGroupId, newUnread: group.unread })
    }

    // 将有新消息的群聊移到列表顶部（只有当不在第一位时才移动）
    if (groupIndex > 0) {
      const updatedGroup = groupConversations.value.splice(groupIndex, 1)[0]
      groupConversations.value.unshift(updatedGroup)
      console.log('群聊已移到列表顶部:', targetGroupId)
    }

    console.log('群聊最后消息更新完成:', {
      groupId: targetGroupId,
      groupName: group.name,
      oldLastMessage,
      newLastMessage: group.lastMessage,
      oldLastTime,
      newLastTime: group.lastTime,
      unread: group.unread
    })
  } else {
    console.warn('未找到对应的群聊:', { targetGroupId, availableGroups: groupConversations.value.map(g => ({ id: g.originalData?.id || g.originalData?.ID, name: g.name })) })
  }
}

// 更新好友列表中的最后一条消息
const updateFriendLastMessage = (userId, messageData) => {
  console.log('开始更新好友最后消息:', { userId, messageData })

  // 确保userId是字符串或数字类型
  const targetUserId = String(userId)

  const friendIndex = friendConversations.value.findIndex(
    friend => String(friend.originalData?.id || friend.originalData?.ID) === targetUserId
  )

  console.log('查找好友索引:', { targetUserId, friendIndex, totalFriends: friendConversations.value.length })

  if (friendIndex !== -1) {
    const friend = friendConversations.value[friendIndex]
    const oldLastMessage = friend.lastMessage
    const oldLastTime = friend.lastTime

    // 更新最后消息内容和时间
    friend.lastMessage = formatMessageContent(
      messageData.msg || messageData.content,
      messageData.typecode2 || messageData.type || 0
    )
    friend.lastTime = formatTime(messageData.t || messageData.timestamp || new Date())

    // 如果不是当前选中的好友会话，增加未读数量
    if (selectedConversation.value?.id !== friend.id) {
      friend.unread = (friend.unread || 0) + 1
      console.log('增加好友未读数量:', { userId: targetUserId, newUnread: friend.unread })
    }

    // 将有新消息的好友会话移到列表顶部（只有当不在第一位时才移动）
    if (friendIndex > 0) {
      const updatedFriend = friendConversations.value.splice(friendIndex, 1)[0]
      friendConversations.value.unshift(updatedFriend)
      console.log('好友会话已移到列表顶部:', targetUserId)
    }

    console.log('好友最后消息更新完成:', {
      userId: targetUserId,
      friendName: friend.name,
      oldLastMessage,
      newLastMessage: friend.lastMessage,
      oldLastTime,
      newLastTime: friend.lastTime,
      unread: friend.unread
    })
  } else {
    console.warn('未找到对应的好友会话:', { targetUserId, availableFriends: friendConversations.value.map(f => ({ id: f.originalData?.id || f.originalData?.ID, name: f.name })) })
  }
}

// 监听自定义群聊消息更新事件
const handleGroupMessageUpdate = (event) => {
  try {
    const { groupId, messageData } = event.detail || {}
    console.log('收到群聊消息更新事件:', { groupId, messageData, eventDetail: event.detail })

    if (!groupId || !messageData) {
      console.warn('群聊消息更新事件数据不完整:', event.detail)
      return
    }

    updateGroupLastMessage(groupId, messageData)
  } catch (error) {
    console.error('处理群聊消息更新事件失败:', error, event)
  }
}

// 监听自定义好友消息更新事件
const handlePrivateMessageUpdate = (event) => {
  try {
    const { userId, messageData } = event.detail || {}
    console.log('收到好友消息更新事件:', { userId, messageData, eventDetail: event.detail })

    if (!userId || !messageData) {
      console.warn('好友消息更新事件数据不完整:', event.detail)
      return
    }

    updateFriendLastMessage(userId, messageData)
  } catch (error) {
    console.error('处理好友消息更新事件失败:', error, event)
  }
}

// 在组件挂载时添加事件监听器
onMounted(() => {
  window.addEventListener('groupMessageUpdate', handleGroupMessageUpdate)
  window.addEventListener('privateMessageUpdate', handlePrivateMessageUpdate)
  console.log('已添加群聊和好友消息更新事件监听器')
})

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('groupMessageUpdate', handleGroupMessageUpdate)
  window.removeEventListener('privateMessageUpdate', handlePrivateMessageUpdate)
  console.log('已移除群聊和好友消息更新事件监听器')
})

// 监听WebSocket消息更新群聊和好友列表（保留作为备用）
watch(() => webSocketStore.lastMessage, (newMessage) => {
  if (newMessage) {
    if (newMessage.typecode === 2) { // 群聊消息
      const groupId = newMessage.toid || newMessage.groupID
      if (groupId) {
        updateGroupLastMessage(groupId, newMessage)
      }
    } else if (newMessage.typecode === 1) { // 私聊消息
      const userId = newMessage.fromid // 私聊消息的发送者ID
      if (userId) {
        updateFriendLastMessage(userId, newMessage)
      }
    }
  }
}, { deep: true })

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'groups') {
    // 切换到群聊时，自动选择第一个群聊
    if (groupConversations.value.length > 0) {
      selectedConversation.value = groupConversations.value[0]
    } else {
      selectedConversation.value = null
    }
  } else if (newTab === 'friends') {
    // 切换到好友时，自动选择第一个好友会话（如果有的话）
    if (friendConversations.value.length > 0) {
      selectedConversation.value = friendConversations.value[0]
    } else {
      selectedConversation.value = null
    }
  }
}, { immediate: true })

defineExpose({
  open: (user) => {
    console.log('🚀 通过 open 方法打开聊天对话框，用户:', user)
    currentUser.value = user
    activeTab.value = 'groups' // 默认显示群聊
    visible.value = true // 这会触发 watch(visible) 中的初始化逻辑
  },
  close: closeDialog
})
</script>
<style>
.el-dialog.chat-dialog{
  height: 680px !important;
}
.el-dialog{
  border-radius: 20px;
}
.el-dialog__header{
  padding-bottom: 0 !important;
  padding: 0 !important;
}
.el-dialog__body{
  /* height: calc(100% - 60px) !important; */
  height: 100%;
  background: #1f2937;
}
.el-dialog.chat-dialog{
  height: 680px;
  padding: 0;
}
.el-empty {
  --el-empty-description-color: #9ca3af;
}
.el-empty__description p {
  color: #9ca3af !important;
}
</style>
<style lang="scss" scoped>
// 样式已移至 styles/chat.scss 文件中

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1f2937;
  color: white;
  border-bottom: 1px solid #374151;
  border-radius: 10px 10px 0 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 10px;

    .username {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;

    .chat-count {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

/* All layout styles moved to Tailwind CSS classes */
</style>
